import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { DocumentDto, fetchDocuments, UploadContext } from '@/api/documents';

export interface UseNotificationPollingOptions {
  pollingInterval?: number;
  uploadContext?: UploadContext;
  enabled?: boolean;
}

export interface NotificationData {
  hasProcessingCompletedDocuments: boolean;
  processingCompletedDocuments: DocumentDto[];
  lastChecked: Date;
}

/**
 * Hook for polling documents with processingCompleted status to show notification badges
 * This hook only focuses on polling functionality and doesn't include file management
 */
export const useNotificationPolling = (options: UseNotificationPollingOptions = {}) => {
  const { getToken } = useAuth();
  
  const {
    pollingInterval = 5000, // 5 seconds
    uploadContext,
    enabled = true,
  } = options;

  const [notificationData, setNotificationData] = useState<NotificationData>({
    hasProcessingCompletedDocuments: false,
    processingCompletedDocuments: [],
    lastChecked: new Date(),
  });

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const notifiedDocumentsRef = useRef<Set<number>>(new Set());

  const pollForNotifications = useCallback(async () => {
    if (!enabled) return;
    
    const token = await getToken();
    if (!token) return;

    try {
      const documents = await fetchDocuments(token, uploadContext);
      
      // Filter for processingCompleted documents that haven't been displayed yet
      const processingCompletedDocs = documents.filter(
        (doc) =>
          doc.status === 'processingCompleted' &&
          !doc.hasStatusBeenDisplayed &&
          !notifiedDocumentsRef.current.has(doc.id)
      );

      const hasNewProcessingCompletedDocuments = processingCompletedDocs.length > 0;

      setNotificationData({
        hasProcessingCompletedDocuments: hasNewProcessingCompletedDocuments,
        processingCompletedDocuments: processingCompletedDocs,
        lastChecked: new Date(),
      });

      // Track notified documents to avoid showing the same notification multiple times
      processingCompletedDocs.forEach((doc) => {
        notifiedDocumentsRef.current.add(doc.id);
      });

    } catch (error) {
      console.error('Failed to poll for notifications:', error);
    }
  }, [getToken, uploadContext, enabled]);

  // Clear notification data
  const clearNotifications = useCallback(() => {
    setNotificationData({
      hasProcessingCompletedDocuments: false,
      processingCompletedDocuments: [],
      lastChecked: new Date(),
    });
  }, []);

  // Clear tracking for specific documents
  const clearNotificationTracking = useCallback((documentIds?: number[]) => {
    if (documentIds) {
      documentIds.forEach(id => notifiedDocumentsRef.current.delete(id));
    } else {
      notifiedDocumentsRef.current.clear();
    }
  }, []);

  // Set up polling interval
  useEffect(() => {
    if (!enabled) {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      return;
    }

    // Clear existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Start polling
    pollingIntervalRef.current = setInterval(pollForNotifications, pollingInterval);
    
    // Initial poll
    void pollForNotifications();

    // Cleanup on unmount or dependency change
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollForNotifications, pollingInterval, enabled]);

  return {
    notificationData,
    clearNotifications,
    clearNotificationTracking,
    pollForNotifications, // Manual trigger if needed
  };
};
