.container {
  width: 100%;
  height: auto;
  min-height: 158px;
  border: 1px solid var(--colors-gray-200);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-rounded-lg);

  &:not(.mobile) {
    min-width: 360px;
  }

  &.noBorder {
    border: none;
    box-shadow: none;
  }

  &.compact {
    min-height: auto;
  }
}

.title {
  font-family: Quasimoda;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: var(--spacing-2);
  color: var(--colors-gray-900);

  &.addressTitle {
    margin-bottom: var(--spacing-3);
  }
}

.inputContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input {
  font-family: Quasimoda;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-2);
  border: 1px solid var(--colors-gray-200);
  border-radius: var(--border-radius-rounded-lg);
  font-size: 16px;
  line-height: 150%;
  color: var(--colors-black);
  transition: border-color 0.2s ease;

  white-space: pre-wrap;
  min-height: 48px;
  max-height: 144px;
  overflow-y: auto;
  outline: none;
  word-break: break-word;
  text-align: left;
  cursor: text;

  &:focus {
    outline: none;
    border-color: var(--colors-gray-500);
  }

  &::placeholder {
    color: var(--colors-gray-500);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.verificationInput {
    text-align: center;

    &:not(:placeholder-shown) {
      letter-spacing: 4px;
    }
  }

  &.textarea {
    min-height: 48px;
    max-height: 144px;
    resize: none;
    overflow-y: auto;

    &[contenteditable='true']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-600);
      cursor: text;
      pointer-events: none;
      position: relative;
    }

    &[contenteditable='false']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-500);
      cursor: not-allowed;
      pointer-events: none;
      position: relative;
    }

    &::-webkit-scrollbar {
      width: 3px;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--colors-gray-300);
      border-radius: 1.5px;
      min-height: 40px;
      max-height: 40px;
      margin: 16px 0;
      cursor: pointer;
    }
  }

  &.accessInstructionDiv {
    min-height: 48px;
    max-height: 144px;
    overflow-y: auto;

    &[contenteditable='true']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-500);
      cursor: text;
      pointer-events: none;
      position: relative;
    }

    &[contenteditable='false']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-500);
      cursor: not-allowed;
      pointer-events: none;
      position: relative;
    }

    &::-webkit-scrollbar {
      width: 3px;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--colors-gray-300);
      border-radius: 1.5px;
      min-height: 40px;
      max-height: 40px;
      margin: 16px 0;
      cursor: pointer;
    }
  }

  &.phoneInput,
  &.selectInput,
  &.addressInput {
    white-space: pre-wrap;
    min-height: 48px;
    max-height: 144px;
    resize: none;
    overflow-y: auto;
    outline: none;
    word-break: break-word;
    text-align: left;
    cursor: text;

    br {
      display: none;
    }

    &.empty[contenteditable='true']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-500);
      cursor: text;
      pointer-events: none;
      position: relative;
    }

    &.empty[contenteditable='false']:empty:before {
      content: attr(data-placeholder);
      color: var(--colors-gray-500);
      cursor: text;
      pointer-events: none;
      position: relative;
    }

    &::-webkit-scrollbar {
      width: 3px;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      margin: 16px 0;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--colors-gray-300);
      border-radius: 1.5px;
      min-height: 40px;
      max-height: 40px;
      margin: 16px 0;
      cursor: pointer;
    }
  }
}

.actionsContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;

  &.buttonLeft {
    justify-content: flex-start;
  }
}

.selectContainer {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.dropdown {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--colors-white);
  border: 1px solid var(--colors-gray-200);
  border-radius: var(--border-radius-rounded-lg);
  z-index: 10;

  &.dropdownBottom {
    top: 100%;
    margin-top: var(--spacing-1);
  }

  &.dropdownTop {
    bottom: 100%;
    margin-bottom: var(--spacing-1);
  }

  &::-webkit-scrollbar {
    width: 3px;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--colors-neutral-200);
    border-radius: 1.5px;
    min-height: 40px;
    max-height: 40px;
    margin: 16px 10px;
    cursor: pointer;
  }
}

.option {
  padding: var(--spacing-3) var(--spacing-2);
  cursor: pointer;
  font-family: Quasimoda;
  font-size: 16px;
  line-height: 150%;
  color: var(--colors-gray-900);

  &:hover {
    background-color: var(--colors-gray-50);
  }
}

.manualEntry {
  font-family: Quasimoda;
  font-size: 16px;
  line-height: 150%;
  color: #1b6e5a;
  cursor: pointer;
  padding: var(--spacing-1) 0;
  white-space: nowrap;

  &:hover {
    text-decoration: none;
  }

  &.addressLookup {
    margin-bottom: 0;
    margin-left: 0;
  }
}

.selectInput {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  cursor: pointer;
  min-height: 48px;
  resize: none;
  padding-right: 30px;

  &::-webkit-scrollbar {
    width: 0;
    display: none;
  }

  span {
    color: var(--colors-gray-500);
    white-space: normal;
    word-wrap: break-word;
    padding-right: var(--spacing-2);
    flex: 1;
  }

  svg {
    margin-top: var(--spacing-0-5);
  }

  &.hasValue span {
    color: var(--colors-black);
  }

  &.empty:empty:before {
    content: attr(data-placeholder);
    color: var(--colors-gray-500);
    cursor: text;
    pointer-events: none;
    position: relative;
  }
}

.addressFields {
  min-height: 48px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-family: Quasimoda;
    font-size: 16px;
    font-weight: normal;
    color: var(--colors-gray-900);
  }
}

.iconInsideInput {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.verificationContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.phoneVerificationInfo {
  font-family: Quasimoda;
  color: var(--colors-gray-700);
  margin-bottom: 4px;
}

.verificationError {
  font-family: Quasimoda;
  font-size: 14px;
  color: var(--colors-red-500);
  margin-top: -8px;
}

.verificationActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 8px;
}

.countdownText {
  font-size: 14px;
  color: var(--colors-gray-500);
  text-align: right;
}

.codeFieldContainer {
  width: 100%;
  margin: 8px 0;
}

.codeFieldInputs {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  width: 100%;

  @media (max-width: 768px) {
    gap: 4px;
    justify-content: center;
  }
}

.codeFieldInput {
  width: 46px;
  height: 46px;
  text-align: center;
  font-size: 20px;
  border: 1px solid var(--colors-gray-200);
  border-radius: var(--border-radius-rounded-lg);
  padding: 0;
  box-sizing: border-box;
  outline: none;

  &:focus {
    border-color: var(--colors-gray-500);
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: 40px;
    height: 40px;
    font-size: 16px;
  }

  @media (max-width: 480px) {
    max-width: 32px;
    height: 32px;
    font-size: 14px;
  }

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type='number'] {
    -moz-appearance: textfield;
  }
}