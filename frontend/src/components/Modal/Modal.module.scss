.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: var(--colors-white);
  border-radius: 24px;
  width: 100%;
  max-width: 693px;
  min-width: 693px;
  height: auto;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.modalInner {
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #222;
  z-index: 2;
  margin-top: 16px;
}

.header {
  display: flex;
  padding: 0 16px 0;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  background: transparent;
  z-index: 5;
}

.title {
  font-family: <PERSON>ua<PERSON><PERSON><PERSON>, Arial, sans-serif;
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  margin-bottom: 20px;
  margin-top: 24px;
  color: var(--colors-black);
}

.content {
  padding: 0 24px 0;
}

.actions {
  padding: 8px 24px 24px;
  gap: 12px;
}

@media (max-width: 768px) {
  .modal,
  .modal.mobile {
    width: calc(100% - 32px);
    min-width: 360px;
    max-width: calc(100% - 32px);
    margin: 83px 16px 52px;
    border-radius: 24px;
  }

  .modalInner {
    max-height: calc(100vh - 200px);
  }

  .header {
    padding: 0 16px 0;
    align-items: flex-start;
  }

  .content {
    padding: 0 16px 0px;
  }

  .actions {
    padding: 8px 24px 24px;
  }
}
