import { create } from 'zustand';

interface User {
  id: string;
  email: string;
  name?: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  setAuth: (isAuthenticated: boolean, user: User | null) => void;
  logout: () => void;
  setToken: (token: string) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  isAuthenticated: false,
  user: null,
  token: null,
  setAuth: (isAuthenticated, user) => set({ isAuthenticated, user }),
  setToken: (token) => set({ token }),
  logout: () => set({ isAuthenticated: false, user: null }),
}));
